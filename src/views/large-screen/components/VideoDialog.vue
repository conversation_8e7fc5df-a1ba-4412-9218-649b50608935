<template>
  <!-- 视频对话框 -->
  <el-dialog
    v-if="!isMinimized"
    v-model="dialogVisible"
    :show-close="false"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    class="large-screen-video-dialog"
    width="1840px"
    :style="{
      border: 'none',
      outline: 'none',
      boxShadow: 'none',
      background: 'transparent'
    }"
  >
    <template #header>
      <div class="dialog-header">
        <div class="title">
          <el-icon><VideoCamera /></el-icon>
          <span>实时监控</span>
        </div>
        <div v-if="props.devices.length > 1" class="video-tabs">
          <el-radio-group v-model="currentVideoIndex">
            <el-radio-button
              v-for="(device, index) in props.devices"
              :key="index"
              :label="index"
            >
              {{ device.deviceName }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="dialog-controls">
          <el-button @click="toggleMinimize">
            <el-icon><Remove /></el-icon>
          </el-button>
          <el-button class="close-button" @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>
    <div class="video-container">
      <video
        ref="videoRef"
        class="video-player"
        controls
        autoplay
        muted
        webkit-playsinline
        playsinline
      />
    </div>
    <!-- WebRtc组件放在video-container外部，避免覆盖视频内容 -->
    <div class="webrtc-panel">
      <WebRtc
        v-if="visible && devices[currentVideoIndex]"
        ref="webRtcRef"
        :roomId="devices[currentVideoIndex].equipmentNumber"
      />
    </div>
  </el-dialog>

  <!-- 最小化时显示的浮动窗口 -->
  <div v-else class="minimized-video" @click="toggleMinimize">
    <video
      ref="minimizedVideoRef"
      class="video-player"
      muted
      autoplay
      webkit-playsinline
      playsinline
    />
    <div class="minimized-controls">
      <span>实时监控</span>
      <el-button @click.stop="toggleMinimize">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import WebRtc from "../../../components/WebRtc/index.vue";
import { Close, Remove, VideoCamera } from "@element-plus/icons-vue";
import flvjs from "flv.js";
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue";

const props = defineProps<{
  devices: Array<{
    id: number;
    deviceName: string;
    streamKey: string;
    equipmentNumber: string;
  }>;
  visible: boolean;
}>();

const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();

const dialogVisible = ref(props.visible);
const videoRef = ref<HTMLVideoElement | null>(null);
const minimizedVideoRef = ref<HTMLVideoElement | null>(null);
const isMinimized = ref(false);
const currentVideoIndex = ref(0);
let flvPlayer: flvjs.Player | null = null;

// 初始化播放器
const initPlayer = () => {
  if (!flvjs.isSupported()) {
    console.error("浏览器不支持 flv.js");
    return;
  }

  const currentDevice = props.devices[currentVideoIndex.value];

  if (!currentDevice?.streamKey || !videoRef.value) {
    console.error("无效的视频流地址或视频元素未就绪");
    return;
  }

  try {
    // 确保之前的播放器已经销毁
    destroyPlayer();

    flvPlayer = flvjs.createPlayer({
      type: "flv",
      url: currentDevice.streamKey,
      isLive: true,
      hasAudio: false,
      hasVideo: true,
      cors: true,
      withCredentials: false
    });

    // 添加错误事件监听
    flvPlayer.on(flvjs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
      console.error("FLV播放器错误:", { errorType, errorDetail, errorInfo });

      // 处理CORS错误
      if (errorDetail === flvjs.ErrorDetails.NETWORK_EXCEPTION) {
        console.warn("网络异常，可能是CORS问题，尝试重新加载...");
        // 可以在这里添加重试逻辑或显示错误提示
      }
    });

    flvPlayer.attachMediaElement(videoRef.value);
    flvPlayer.load();

    // 添加延时确保加载完成
    setTimeout(() => {
      if (flvPlayer) {
        (flvPlayer.play() as Promise<void>).catch(err => {
          console.error("视频播放失败:", err);
          // 可以在这里显示用户友好的错误提示
        });
      }
    }, 100);
  } catch (error) {
    console.error("初始化播放器失败:", error);
    // 可以在这里显示用户友好的错误提示
  }
};

// 销毁播放器
const destroyPlayer = () => {
  if (flvPlayer) {
    try {
      flvPlayer.pause();
      flvPlayer.unload();
      flvPlayer.detachMediaElement();
      flvPlayer.destroy();
    } catch (error) {
      console.error("销毁播放器失败:", error);
    } finally {
      flvPlayer = null;
    }
  }
};

// 处理对话框关闭
const handleClose = () => {
  // 先销毁播放器
  destroyPlayer();
  emit("update:visible", false);
};

// 切换最小化状态
const toggleMinimize = async () => {
  // 先销毁播放器
  destroyPlayer();
  isMinimized.value = !isMinimized.value;
  await nextTick();
  initPlayer();
};

onMounted(() => {
  if (props.visible) {
    initPlayer();
  }
});

onBeforeUnmount(() => {
  destroyPlayer();
});

// 监听对话框显示状态
watch(
  () => props.visible,
  async val => {
    dialogVisible.value = val;
    if (val) {
      // 对话框显示时初始化播放器
      await nextTick();
      initPlayer();
    } else {
      // 对话框关闭时销毁播放器
      destroyPlayer();
    }
  }
);

// 监听当前视频索引变化
watch(currentVideoIndex, () => {
  destroyPlayer();
  initPlayer();
});
</script>

<style lang="scss">
// 全局样式 - 彻底移除Element Plus对话框的默认白色边框并垂直居中
.el-dialog.large-screen-video-dialog,
.el-dialog__wrapper .large-screen-video-dialog,
.el-overlay .large-screen-video-dialog {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.large-screen-video-dialog {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;

  .el-dialog {
    border: none !important;
    outline: none !important;
    margin: 0 !important;
    box-shadow: none !important;
    width: 1840px !important;
    height: 1074px !important;
  }

  .el-dialog__header {
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .el-dialog__body {
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

// 更强的覆盖 - 针对Element Plus对话框的具体样式
.el-dialog.large-screen-video-dialog .el-dialog {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

// 最强覆盖 - 使用更高优先级的选择器
.el-overlay .el-overlay-dialog .large-screen-video-dialog,
.el-dialog__wrapper .large-screen-video-dialog,
body .large-screen-video-dialog {
  width: 1840px !important;
  height: 1074px !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

// 针对Element Plus对话框包装器的强制覆盖
.el-overlay,
.el-overlay .el-overlay-dialog,
.el-dialog__wrapper {
  .large-screen-video-dialog {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    margin-top: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 0 !important;
  }
}

// 终极覆盖 - 使用最高优先级
html body .el-overlay .large-screen-video-dialog,
html body .el-dialog__wrapper .large-screen-video-dialog,
html body .large-screen-video-dialog.el-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 1840px !important;
  height: 1074px !important;
  margin: 0 !important;
  margin-top: 0 !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  margin-bottom: 0 !important;
}
</style>

<style lang="scss" scoped>
// 导入字体配置
@import "../styles/font-sizes.scss";
:deep(.large-screen-video-dialog) {
  overflow: hidden;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;

  /* 强制设置对话框尺寸 */
  .el-dialog {
    width: 1840px !important;
    height: 1074px !important;
    margin: 0 !important;
  }

  .call-controls {
    right: 125px;
    bottom: 140px;
    transform: scale(2);
  }

  .el-dialog {
    background: linear-gradient(
      135deg,
      rgba(0, 24, 75, 0.95) 0%,
      rgba(0, 41, 102, 0.9) 50%,
      rgba(0, 24, 75, 0.95) 100%
    ) !important;
    backdrop-filter: blur(20px) !important;
    border: none !important;
    border-radius: 16px !important;
    outline: none !important;
    box-shadow:
      0 0 40px rgba(64, 158, 255, 0.3),
      0 0 80px rgba(0, 234, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;

    /* 移除默认边框样式 */
    position: relative;

    /* 强制覆盖可能的白色背景 */
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(0, 24, 75, 0.95) 0%,
        rgba(0, 41, 102, 0.9) 50%,
        rgba(0, 24, 75, 0.95) 100%
      );
      border-radius: 16px;
      z-index: -2;
    }

    &::before {
      content: "";
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: linear-gradient(
        45deg,
        rgba(64, 158, 255, 0.3) 0%,
        rgba(0, 234, 255, 0.5) 25%,
        rgba(64, 158, 255, 0.3) 50%,
        rgba(0, 234, 255, 0.5) 75%,
        rgba(64, 158, 255, 0.3) 100%
      );
      border-radius: 16px;
      z-index: -1;
      animation: borderGlow 4s ease-in-out infinite alternate;
    }

    &__header {
      padding: 0 !important;
      margin: 0 !important;
      border: none !important;
      border-bottom: 1px solid rgba(0, 234, 255, 0.2) !important;
      background: rgba(0, 21, 41, 0.8) !important;
      border-radius: 16px 16px 0 0 !important;
    }

    &__body {
      padding: 0 !important;
      background: rgba(0, 0, 0, 0.4) !important;
      border: none !important;
      border-radius: 0 0 16px 16px !important;
    }
  }
}

/* 移除重复的样式覆盖，已在全局样式中处理 */

@keyframes borderGlow {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.002);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

.dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px 12px; /* 减少padding以节省空间 */
  background: linear-gradient(
    135deg,
    rgba(0, 21, 41, 0.9) 0%,
    rgba(0, 41, 102, 0.8) 50%,
    rgba(0, 21, 41, 0.9) 100%
  );
  border-radius: 16px 16px 0 0;

  &::before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    content: "";
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 234, 255, 0.8),
      rgba(64, 158, 255, 0.6),
      rgba(0, 234, 255, 0.8),
      transparent
    );
    animation: headerGlow 2s ease-in-out infinite alternate;
  }

  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    content: "";
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 234, 255, 0.4),
      transparent
    );
  }

  .title {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: $font-dialog-title; // 使用统一的对话框标题字体大小
    font-weight: 600;
    color: #fff;
    text-shadow:
      0 0 10px rgba(0, 234, 255, 0.6),
      0 0 20px rgba(0, 234, 255, 0.3);

    .el-icon {
      font-size: 28px;
      color: #00eaff;
      filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.8));
      animation: iconPulse 2s ease-in-out infinite;
    }
  }
}

@keyframes headerGlow {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.8));
  }
  50% {
    transform: scale(1.05);
    filter: drop-shadow(0 0 12px rgba(0, 234, 255, 1));
  }
}

.video-tabs {
  display: flex;
  justify-content: center;
  margin: 0 24px;

  ::v-deep .el-radio-group {
    .el-radio-button__inner {
      padding: 10px 20px;
      font-size: $font-dialog-content; // 使用统一的对话框内容字体大小
      font-weight: 500;
      color: rgba(255, 255, 255, 0.9);
      background: linear-gradient(
        135deg,
        rgba(0, 21, 41, 0.6) 0%,
        rgba(0, 41, 102, 0.4) 100%
      );
      border: 1px solid rgba(0, 234, 255, 0.3);
      border-radius: 8px;
      transition: all 0.3s ease;
      backdrop-filter: blur(8px);

      &:hover {
        background: linear-gradient(
          135deg,
          rgba(0, 234, 255, 0.15) 0%,
          rgba(64, 158, 255, 0.1) 100%
        );
        border-color: rgba(0, 234, 255, 0.6);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 234, 255, 0.2);
      }
    }

    .el-radio-button__original-radio:checked + .el-radio-button__inner {
      color: #fff;
      text-shadow: 0 0 10px rgba(0, 234, 255, 0.8);
      background: linear-gradient(
        135deg,
        rgba(0, 234, 255, 0.3) 0%,
        rgba(64, 158, 255, 0.2) 100%
      );
      border-color: #00eaff;
      box-shadow:
        -1px 0 0 0 #00eaff,
        0 0 15px rgba(0, 234, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
    }

    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 8px 0 0 8px;
    }

    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0 8px 8px 0;
    }

    .el-radio-button:only-child .el-radio-button__inner {
      border-radius: 8px;
    }
  }
}

.dialog-controls {
  display: flex;
  gap: 12px;

  ::v-deep(.el-button) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    padding: 0;
    color: rgba(255, 255, 255, 0.8);
    background: linear-gradient(
      135deg,
      rgba(0, 21, 41, 0.6) 0%,
      rgba(0, 41, 102, 0.4) 100%
    );
    border: 1px solid rgba(0, 234, 255, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;

    &:hover {
      color: #fff;
      background: linear-gradient(
        135deg,
        rgba(0, 234, 255, 0.2) 0%,
        rgba(64, 158, 255, 0.15) 100%
      );
      border-color: rgba(0, 234, 255, 0.6);
      transform: translateY(-2px) scale(1.05);
      box-shadow:
        0 6px 20px rgba(0, 234, 255, 0.3),
        0 0 15px rgba(0, 234, 255, 0.2);
    }

    &:active {
      transform: translateY(0) scale(1);
    }

    .el-icon {
      font-size: 20px;
      filter: drop-shadow(0 0 4px rgba(0, 234, 255, 0.4));
    }
  }
}

.close-button {
  ::v-deep(.el-button) {
    .el-icon {
      color: #ff4757;
      filter: drop-shadow(0 0 4px rgba(220, 38, 127, 0.6));
    }
    
    &:hover {
      color: #ff4757;
      background: linear-gradient(
        135deg,
        rgba(220, 38, 127, 0.2) 0%,
        rgba(244, 67, 54, 0.15) 100%
      );
      border-color: rgba(220, 38, 127, 0.6);
      transform: translateY(-2px) scale(1.05);
      box-shadow:
        0 6px 20px rgba(220, 38, 127, 0.3),
        0 0 15px rgba(220, 38, 127, 0.2);

      .el-icon {
        color: #ff4757;
      }
    }
  }
}

.video-container {
  position: relative;
  width: 100%;
  height: 940px; /* 优化后: 1074px - 60px(标题) - 80px(控制面板) = 934px，约940px */
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 21, 41, 0.3) 50%,
    rgba(0, 0, 0, 0.9) 100%
  );
  border-radius: 0; /* 移除底部圆角，与webrtc-panel连接 */

  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 2px;
    content: "";
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 234, 255, 0.4),
      rgba(64, 158, 255, 0.3),
      rgba(0, 234, 255, 0.4),
      transparent
    );
    z-index: 1;
  }

  &::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    content: "";
    background: linear-gradient(
      45deg,
      transparent 48%,
      rgba(0, 234, 255, 0.05) 49%,
      rgba(0, 234, 255, 0.05) 51%,
      transparent 52%
    );
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
    animation: scanlines 3s linear infinite;
  }
}

.webrtc-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 80px; /* 水平布局后的紧凑高度 */
  background: linear-gradient(
    135deg,
    rgba(0, 24, 75, 0.9) 0%,
    rgba(0, 41, 102, 0.8) 50%,
    rgba(0, 24, 75, 0.9) 100%
  );
  backdrop-filter: blur(10px);
  border-radius: 0 0 16px 16px;
  border-top: 1px solid rgba(0, 234, 255, 0.3);
  box-shadow:
    0 -2px 20px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(0, 234, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  /* 科技感边框动画 */
  &::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(
      90deg,
      rgba(0, 234, 255, 0.2),
      rgba(64, 158, 255, 0.4),
      rgba(0, 234, 255, 0.2),
      rgba(64, 158, 255, 0.4)
    );
    border-radius: 0 0 16px 16px;
    z-index: -1;
    animation: webrtcGlow 3s ease-in-out infinite alternate;
  }

  /* 覆盖WebRtc组件的默认样式 - 水平布局 */
  :deep(.webrtc-container) {
    background: transparent;
    flex-direction: row !important;
    align-items: center !important;
    gap: 20px !important;

    .status-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      margin-bottom: 0 !important;
      text-align: left;
      order: 2; /* 文字在右侧 */
    }

    .control-buttons {
      gap: 12px;
      order: 1; /* 按钮在左侧 */
    }

    .control-btn {
      width: 36px;
      height: 36px;
      background: rgba(0, 234, 255, 0.1) !important;
      border: 1px solid rgba(0, 234, 255, 0.3) !important;
      color: #00eaff !important;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(0, 234, 255, 0.2) !important;
        border-color: rgba(0, 234, 255, 0.6) !important;
        transform: scale(1.05);
        box-shadow: 0 0 15px rgba(0, 234, 255, 0.4);
      }

      &.el-button--danger {
        background: rgba(245, 108, 108, 0.1) !important;
        border-color: rgba(245, 108, 108, 0.3) !important;
        color: #f56c6c !important;

        &:hover {
          background: rgba(245, 108, 108, 0.2) !important;
          border-color: rgba(245, 108, 108, 0.6) !important;
          box-shadow: 0 0 15px rgba(245, 108, 108, 0.4);
        }
      }

      &.el-button--success {
        background: rgba(103, 194, 58, 0.1) !important;
        border-color: rgba(103, 194, 58, 0.3) !important;
        color: #67c23a !important;

        &:hover {
          background: rgba(103, 194, 58, 0.2) !important;
          border-color: rgba(103, 194, 58, 0.6) !important;
          box-shadow: 0 0 15px rgba(103, 194, 58, 0.4);
        }
      }
    }
  }
}

@keyframes webrtcGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.8;
  }
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0 0 16px 16px;
  z-index: 2;
}

@keyframes scanlines {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

.minimized-video {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 2000;
  width: 360px;
  aspect-ratio: 16/9;
  overflow: hidden;
  cursor: pointer;
  background: linear-gradient(
    135deg,
    rgba(0, 24, 75, 0.95) 0%,
    rgba(0, 41, 102, 0.9) 50%,
    rgba(0, 24, 75, 0.95) 100%
  );
  backdrop-filter: blur(20px);
  border: 2px solid transparent;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(64, 158, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background-clip: padding-box;

  &::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      45deg,
      rgba(64, 158, 255, 0.6),
      rgba(0, 234, 255, 0.8),
      rgba(64, 158, 255, 0.6)
    );
    border-radius: 12px;
    z-index: -1;
    animation: minimizedBorderGlow 2s ease-in-out infinite alternate;
  }

  &:hover {
    transform: scale(1.05) translateY(-4px);
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.5),
      0 0 60px rgba(0, 234, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

.minimized-controls {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  color: #fff;
  background: linear-gradient(
    135deg,
    rgba(0, 21, 41, 0.9) 0%,
    rgba(0, 41, 102, 0.8) 100%
  );
  border-bottom: 1px solid rgba(0, 234, 255, 0.2);
  border-radius: 12px 12px 0 0;
  backdrop-filter: blur(8px);

  span {
    font-size: $font-dialog-content; // 使用统一的对话框内容字体大小
    font-weight: 600;
    text-shadow:
      0 0 10px rgba(0, 234, 255, 0.6),
      0 0 20px rgba(0, 234, 255, 0.3);
  }

  ::v-deep .el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    padding: 0;
    color: rgba(255, 255, 255, 0.8);
    background: linear-gradient(
      135deg,
      rgba(0, 21, 41, 0.6) 0%,
      rgba(0, 41, 102, 0.4) 100%
    );
    border: 1px solid rgba(0, 234, 255, 0.3);
    border-radius: 8px;
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;

    &:hover {
      color: #ff4757;
      background: linear-gradient(
        135deg,
        rgba(220, 38, 127, 0.2) 0%,
        rgba(244, 67, 54, 0.15) 100%
      );
      border-color: rgba(220, 38, 127, 0.6);
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(220, 38, 127, 0.3);
    }

    .el-icon {
      font-size: 16px;
      filter: drop-shadow(0 0 4px rgba(0, 234, 255, 0.4));
    }
  }
}

@keyframes minimizedBorderGlow {
  0% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
